import {
	Sun,
	<PERSON>,
	Monitor,
	User,
	Settings,
	LogOut,
	Home,
	Cat,
	Info,
	Mail,
	MessageCircleMore,
} from "lucide-react";
import { type Locale } from "@/lib/i18n/routing";

/**
 * Navigation route configuration types
 */
export interface NavRoute {
	label: string;
	href: string;
	icon?: React.ComponentType<{ className?: string }>;
	translationKey: string;
}

export interface UserMenuRoute {
	label: string;
	href: string;
	icon: React.ComponentType<{ className?: string }>;
	translationKey: string;
	translationNamespace?: string;
	action?: () => Promise<void>;
}

export interface AuthRoute {
	label: string;
	href: string;
	translationKey: string;
	variant?: "ghost" | "default";
}

/**
 * Main navigation routes configuration
 */
export const mainNavRoutes: Omit<NavRoute, "label">[] = [
	{
		href: "/",
		icon: Home,
		translationKey: "navigation.home",
	},
	{
		href: "/cats",
		icon: Cat,
		translationKey: "navigation.cats",
	},
	{
		href: "/about",
		icon: Info,
		translationKey: "navigation.about",
	},
	{
		href: "/contact",
		icon: Mail,
		translationKey: "navigation.contact",
	},
];

/**
 * User avatar popup menu routes configuration
 */
export const userMenuRoutes: Omit<UserMenuRoute, "label">[] = [
	{
		href: "/profile",
		icon: User,
		translationKey: "navigation.profile",
		translationNamespace: "common",
	},
	{
		href: "/messages",
		icon: MessageCircleMore,
		translationKey: "messages.title",
		translationNamespace: "profile",
	},
	{
		href: "/profile?tab=settings",
		icon: Settings,
		translationKey: "settings.title",
		translationNamespace: "profile",
	},
];

/**
 * Authentication routes configuration
 */
export const authRoutes: Omit<AuthRoute, "label">[] = [
	{
		href: "/auth/login",
		translationKey: "navigation.login",
		variant: "ghost",
	},
	{
		href: "/auth/register",
		translationKey: "navigation.register",
		variant: "default",
	},
];

/**
 * Language configuration for navbar components
 */
export const languageConfig = {
	// Language display names
	names: {
		en: "English",
		fr: "Français",
		ar: "العربية",
	} as Record<Locale, string>,

	// Language flags (using emoji flags)
	flags: {
		en: "🇬🇧",
		fr: "🇫🇷",
		ar: "🇩🇿",
	} as Record<Locale, string>,
} as const;

/**
 * Theme configuration for navbar components
 */
export const themeConfig = {
	options: [
		{ key: "light", icon: Sun },
		{ key: "dark", icon: Moon },
		{ key: "system", icon: Monitor },
	] as const,
} as const;

/**
 * Helper functions to get routes with translated labels
 */
export const getMainNavRoutes = (t: (key: string) => string): NavRoute[] => {
	return mainNavRoutes.map((route) => ({
		...route,
		label: t(route.translationKey),
	}));
};

export const getUserMenuRoutes = (
	tCommon: (key: string) => string,
	tProfile: (key: string) => string
): UserMenuRoute[] => {
	return userMenuRoutes.map((route) => ({
		...route,
		label:
			route.translationNamespace === "profile"
				? tProfile(route.translationKey)
				: tCommon(route.translationKey),
	}));
};

export const getAuthRoutes = (t: (key: string) => string): AuthRoute[] => {
	return authRoutes.map((route) => ({
		...route,
		label: t(route.translationKey),
	}));
};

/**
 * Helper function to get theme options with translated labels
 */
export const getThemeOptions = (tTheme: (key: string) => string) => {
	return themeConfig.options.map((option) => ({
		...option,
		label: tTheme(option.key),
	}));
};

/**
 * Logout action configuration
 */
export const logoutRoute: Omit<UserMenuRoute, "label"> = {
	href: "#",
	icon: LogOut,
	translationKey: "navigation.logout",
	translationNamespace: "common",
};
