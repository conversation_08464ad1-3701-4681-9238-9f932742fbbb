import { z } from "zod";
import { createTRPCRouter as router, publicProcedure } from "../trpc";

export const breedsRouter = router({
	getAll: publicProcedure.query(async ({ ctx }) => {
		const breeds = await ctx.db.query.catBreeds.findMany({
			orderBy: (catBreeds, { asc }) => [asc(catBreeds.name)],
		});

		return breeds;
	}),

	getById: publicProcedure.input(z.number()).query(async ({ ctx, input }) => {
		const breed = await ctx.db.query.catBreeds.findFirst({
			where: (catBreeds, { eq }) => eq(catBreeds.id, input),
		});

		return breed;
	}),
});
