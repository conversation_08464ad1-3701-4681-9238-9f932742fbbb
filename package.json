{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:migrate": "tsx lib/db/migrate.ts", "db:studio": "drizzle-kit studio", "db:seed": "tsx lib/db/seed.ts", "db:seed-location": "tsx lib/db/seed-location.ts", "db:migrate-slugs": "tsx lib/db/manual-slug-migration.ts"}, "dependencies": {"@auth/drizzle-adapter": "^1.9.1", "@better-auth/cli": "^1.2.8", "@formatjs/intl-localematcher": "^0.6.1", "@hookform/resolvers": "latest", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-aspect-ratio": "1.1.7", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-checkbox": "1.3.2", "@radix-ui/react-collapsible": "1.1.11", "@radix-ui/react-context-menu": "2.2.15", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-hover-card": "1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "1.1.15", "@radix-ui/react-navigation-menu": "1.2.13", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-progress": "1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slider": "1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-toast": "1.2.14", "@radix-ui/react-toggle": "1.1.9", "@radix-ui/react-toggle-group": "1.1.10", "@radix-ui/react-tooltip": "1.2.7", "@stepperize/react": "^5.1.6", "@t3-oss/env-nextjs": "^0.13.6", "@tanstack/react-query": "^5.83.0", "@trpc/client": "^11.4.3", "@trpc/react-query": "latest", "@trpc/server": "^11.4.3", "@trpc/tanstack-react-query": "^11.4.3", "@types/bcryptjs": "^3.0.0", "@types/better-sqlite3": "latest", "@types/pg": "latest", "@types/sql.js": "latest", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "bcrypt": "latest", "bcryptjs": "^3.0.2", "better-auth": "^1.3.0", "bun-types": "latest", "class-variance-authority": "^0.7.1", "client-only": "^0.0.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "4.1.0", "drizzle-orm": "latest", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.515.0", "negotiator": "^1.0.0", "next": "15.4.2", "next-intl": "^4.3.1", "next-themes": "^0.4.4", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-dropzone": "^14.3.8", "react-easy-crop": "^5.4.2", "react-hook-form": "latest", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.3", "resend": "^4.5.1", "server-only": "^0.0.1", "sonner": "^1.7.1", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.6", "zod": "^4.0.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/bcrypt": "latest", "@types/negotiator": "^0.6.4", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.1", "postcss": "^8", "tailwindcss": "^4.1.10", "tsx": "latest", "typescript": "^5"}}