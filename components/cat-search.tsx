"use client";

import { useState, useEffect } from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useDebounce } from "@/hooks/use-debounce";
import { cn } from "@/lib/utils";

export function CatSearch() {
	const router = useRouter();
	const pathname = usePathname();
	const searchParams = useSearchParams();
	const t = useTranslations("cats");

	// Initialize input value from URL parameters
	const [inputValue, setInputValue] = useState(() => {
		return searchParams.get("search") || "";
	});

	// Debounce search input for live search
	const debouncedSearch = useDebounce(inputValue, 500);

	// Sync input value with URL parameters when they change
	useEffect(() => {
		const urlSearch = searchParams.get("search") || "";
		if (urlSearch !== inputValue) {
			setInputValue(urlSearch);
		}
	}, [searchParams]);

	// Handle live search with debounced input
	useEffect(() => {
		const currentUrlSearch = searchParams.get("search") || "";

		// Trigger search if debounced value differs from current URL parameter
		if (debouncedSearch !== currentUrlSearch) {
			handleSearch(debouncedSearch);
		}
	}, [debouncedSearch]);

	const handleSearch = (value: string) => {
		const params = new URLSearchParams(searchParams.toString());

		if (value.trim()) {
			params.set("search", value.trim());
		} else {
			params.delete("search");
		}

		// Reset to first page when searching
		params.delete("page");

		router.push(`${pathname}?${params.toString()}`);
	};

	const clearSearch = () => {
		setInputValue("");
		handleSearch("");
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			e.preventDefault();
			handleSearch(inputValue);
		}
	};

	return (
		<div className="relative w-full max-w-sm sm:max-w-md lg:max-w-lg">
			{/* Search Icon - Fixed positioning with proper spacing */}
			<div className="absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none z-20 flex items-center justify-center">
				<Search className="h-4 w-4 text-muted-foreground shrink-0" />
			</div>

			{/* Input Field - Mobile-first responsive design */}
			<Input
				placeholder={t("search.placeholder")}
				value={inputValue}
				onChange={(e) => setInputValue(e.target.value)}
				onKeyDown={handleKeyDown}
				className={cn(
					// Base styles - mobile first
					"w-full min-w-0 h-10 text-sm",
					"pl-10 pr-12", // Space for icons
					"border border-input bg-background",
					"placeholder:text-muted-foreground",
					"focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
					"transition-all duration-200",
					"rounded-md",
					// Small screens and up
					"sm:h-11 sm:text-base sm:pr-14",
					// Medium screens and up
					"md:text-sm",
					// Ensure proper overflow handling
					"overflow-hidden text-ellipsis"
				)}
			/>

			{/* Clear Button - Adequate touch target with proper positioning */}
			{inputValue && (
				<div className="absolute right-1 top-1/2 -translate-y-1/2 z-20 flex items-center justify-center">
					<Button
						variant="ghost"
						size="sm"
						onClick={clearSearch}
						aria-label={t("search.clear")}
						className={cn(
							// Base mobile styles - minimum 44px touch target
							"h-8 w-8 min-h-[44px] min-w-[44px] p-0",
							"rounded-md shrink-0",
							"hover:bg-muted focus:bg-muted",
							"focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-1",
							"transition-colors duration-200",
							"touch-manipulation",
							// Small screens and up
							"sm:h-9 sm:w-9 sm:min-h-[36px] sm:min-w-[36px]",
							// Ensure button doesn't interfere with input
							"flex items-center justify-center"
						)}
					>
						<X className="h-3.5 w-3.5 sm:h-4 sm:w-4 shrink-0" />
					</Button>
				</div>
			)}
		</div>
	);
}
