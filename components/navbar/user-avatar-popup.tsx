"use client";

import { <PERSON> } from "@/lib/i18n/navigation";
import { signOut } from "@/lib/auth/client";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useTranslations, useLocale } from "next-intl";
import { cn } from "@/lib/utils";
import { getUserMenuRoutes, logoutRoute } from "@/lib/config/navbar-config";

interface UserAvatarPopupProps {
	user: any;
}

export function UserAvatarPopup({ user }: UserAvatarPopupProps) {
	const t = useTranslations("common");
	const tProfile = useTranslations("profile");
	const currentLocale = useLocale();
	const isRTL = currentLocale === "ar";

	// Get menu routes from centralized configuration
	const menuRoutes = getUserMenuRoutes(t, tProfile);
	const logout = {
		...logoutRoute,
		label: t(logoutRoute.translationKey),
	};

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button
					variant="ghost"
					className="relative h-10 w-10 rounded-full cursor-pointer"
				>
					<Avatar className="h-10 w-10">
						<AvatarImage
							src={user?.image || "/avatar-placeholder.png"}
							alt={user?.name || "User"}
						/>
						<AvatarFallback className="bg-teal-100 text-teal-700 dark:bg-teal-900 dark:text-teal-300">
							{(user?.name || "U").charAt(0)}
						</AvatarFallback>
					</Avatar>
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent
				className={cn("w-72", isRTL && "rtl")}
				align="end"
				forceMount
			>
				<div
					className={cn(
						"flex items-center gap-3 p-4",
						isRTL ? "justify-end flex-row-reverse" : "justify-start"
					)}
				>
					<Avatar className="h-12 w-12">
						<AvatarImage
							src={user?.image || "/avatar-placeholder.png"}
							alt={user?.name || "User"}
						/>
						<AvatarFallback className="bg-teal-100 text-teal-700 dark:bg-teal-900 dark:text-teal-300">
							{(user?.name || "U").charAt(0)}
						</AvatarFallback>
					</Avatar>
					<div
						className={cn(
							"flex flex-col space-y-1 leading-none min-w-0 flex-1",
							isRTL ? "text-right" : "text-left"
						)}
					>
						<p className="font-semibold text-foreground truncate">
							{user?.name || "User"}
						</p>
						<p
							className="text-xs text-muted-foreground break-all"
							title={user?.email}
						>
							{user?.email}
						</p>
					</div>
				</div>
				<DropdownMenuSeparator />
				{menuRoutes.map((route) => {
					const Icon = route.icon;
					return (
						<DropdownMenuItem key={route.href} asChild>
							<Link
								href={route.href}
								className={cn(
									"flex items-center gap-3 px-4 py-3",
									isRTL && "flex-row-reverse"
								)}
							>
								<Icon className="h-4 w-4" />
								<span>{route.label}</span>
							</Link>
						</DropdownMenuItem>
					);
				})}
				<DropdownMenuSeparator />
				<DropdownMenuItem
					className={cn(
						"flex items-center gap-3 px-4 py-3 text-destructive focus:text-destructive cursor-pointer",
						isRTL && "flex-row-reverse"
					)}
					onSelect={async (event) => {
						event.preventDefault();
						await signOut();
						window.location.reload();
					}}
				>
					{(() => {
						const LogoutIcon = logout.icon;
						return <LogoutIcon className="h-4 w-4" />;
					})()}
					<span>{logout.label}</span>
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
