"use client";

import { Link } from "@/lib/i18n/navigation";
import { usePathname } from "@/lib/i18n/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useSession } from "@/lib/auth/client";
import { Skeleton } from "@/components/ui/skeleton";
import { api } from "@/lib/trpc/react";
import { useTranslations, useLocale } from "next-intl";
import { useState } from "react";
import { UserAvatarPopup } from "./user-avatar-popup";
import { ThemeSwitcherPopup } from "./theme-switcher-popup";
import { LanguageSwitcherPopup } from "./language-switcher-popup";
import { MobileNavigation } from "./mobile-navigation";
import { getMainNavRoutes, getAuthRoutes } from "@/lib/config/navbar-config";

export function Navbar() {
	const pathname = usePathname();
	const { data: session, isPending: isSessionLoading } = useSession();
	const t = useTranslations("common");
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
	const currentLocale = useLocale();
	const isRTL = currentLocale === "ar";

	// Get routes from centralized configuration
	const routes = getMainNavRoutes(t);
	const authRoutes = getAuthRoutes(t);

	// Fetch user profile from tRPC if authenticated
	const { data: userData } = api.users.getProfile.useQuery(undefined, {
		enabled: !!session?.user,
		refetchOnWindowFocus: true,
	});

	// Use userData if available, fallback to session.user for auth check
	const user = userData || session?.user;

	return (
		<nav
			className={cn(
				"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur-sm supports-backdrop-filter:bg-background/60",
				isRTL && "rtl"
			)}
		>
			<div className="flex h-16 w-full items-center px-4 md:px-6">
				{/* Logo */}
				<Link
					href="/"
					className={cn(
						"flex items-center space-x-2 font-bold",
						isRTL ? "ml-8 space-x-reverse" : "mr-8"
					)}
				>
					<span>🐱 {t("appName")}</span>
				</Link>

				{/* Desktop Navigation */}
				<div
					className={cn(
						"hidden md:flex gap-6",
						isRTL && "flex-row-reverse"
					)}
				>
					{routes.map((route) => (
						<Link
							key={route.href}
							href={route.href}
							className={cn(
								"text-sm font-medium transition-colors hover:text-foreground/80 px-3 py-2 rounded-md",
								pathname === route.href
									? "text-foreground bg-accent"
									: "text-foreground/60 hover:text-foreground"
							)}
						>
							{route.label}
						</Link>
					))}
				</div>

				{/* Right/Left Side Actions (RTL aware) */}
				<div
					className={cn(
						"flex items-center gap-2",
						isRTL ? "mr-auto" : "ml-auto"
					)}
				>
					{/* Desktop Controls */}
					<div className="hidden md:flex items-center gap-2">
						<LanguageSwitcherPopup />
						<ThemeSwitcherPopup />
					</div>

					{/* User Authentication */}
					{isSessionLoading ? (
						<Skeleton className="h-10 w-10 rounded-full bg-gray-200" />
					) : user ? (
						<UserAvatarPopup user={user} />
					) : (
						<div className="hidden md:flex items-center gap-2">
							{authRoutes.map((authRoute) => (
								<Button
									key={authRoute.href}
									variant={authRoute.variant}
									size="sm"
									asChild
								>
									<Link href={authRoute.href}>
										{authRoute.label}
									</Link>
								</Button>
							))}
						</div>
					)}

					{/* Mobile Menu */}
					<MobileNavigation
						routes={routes}
						isOpen={isMobileMenuOpen}
						onOpenChange={setIsMobileMenuOpen}
						user={user}
						isRTL={isRTL}
					/>
				</div>
			</div>
		</nav>
	);
}
