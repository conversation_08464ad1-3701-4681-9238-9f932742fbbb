"use client";

import Image from "next/image";
import { useTranslations, useLocale } from "next-intl";
import { Link } from "@/lib/i18n/navigation";
import { cn } from "@/lib/utils";
import { Cat, Settings, Trash2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CatStatusMenu } from "@/components/cat-status-menu";

interface CatData {
	id: string;
	name: string;
	age: string;
	breed: string;
	gender: string; // Allow any string to match the API response
	location: string;
	imageUrl?: string;
	adopted?: boolean | null;
	isDraft?: boolean | null;
	status?: "available" | "pending" | "adopted" | "unavailable";
}

interface CatCardProps {
	cat: CatData;
	showActions?: boolean;
	onDelete?: (cat: { id: string; name: string }) => void;
	onStatusChange?: (id: string, status: string) => void;
	isDeletingCat?: boolean;
}

export function ProfileCatCard({
	cat,
	showActions = false,
	onDelete,
	onStatusChange,
	isDeletingCat = false,
}: CatCardProps) {
	const locale = useLocale();
	const isRTL = locale === "ar";
	const catsT = useTranslations("cats");
	const buttonT = useTranslations("buttons");

	return (
		<div className="bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-md transition-shadow">
			<div className="flex flex-col md:flex-row gap-6">
				{/* Cat Image */}
				<div className="md:w-48 h-36 rounded-xl overflow-hidden bg-gray-100 flex-shrink-0">
					<Image
						src={cat.imageUrl || "/cat.jpeg"}
						alt={cat.name}
						width={192}
						height={144}
						className="w-full h-full object-cover"
						onError={(e) => {
							e.currentTarget.src = "/cat.jpeg";
						}}
					/>
				</div>

				{/* Cat Info */}
				<div className="flex-1">
					<div className="flex justify-between items-start mb-4">
						<div>
							<h3 className="text-xl font-display font-semibold text-gray-900 mb-1">
								{cat.name}
							</h3>
							<p className="text-gray-600 mb-2">
								{cat.age} • {cat.breed} •{" "}
								{cat.gender === "male"
									? catsT("filters.male")
									: catsT("filters.female")}
							</p>
							<p className="text-gray-700">{cat.location}</p>
						</div>
					</div>

					{/* Actions */}
					{showActions && (
						<div className="flex flex-wrap gap-3">
							<Link
								href={`/cats/${cat.id}`}
								className={cn(
									"inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors min-h-[44px]",
									isRTL
										? "space-x-reverse space-x-2"
										: "space-x-2"
								)}
							>
								<Cat className="w-4 h-4" />
								<span>{catsT("card.viewDetails")}</span>
							</Link>
							<Link
								href={`/cats/${cat.id}/edit`}
								className={cn(
									"inline-flex items-center px-4 py-2 bg-teal-50 hover:bg-teal-100 text-teal-700 rounded-lg font-medium transition-colors min-h-[44px]",
									isRTL
										? "space-x-reverse space-x-2"
										: "space-x-2"
								)}
							>
								<Settings className="w-4 h-4" />
								<span>{catsT("card.edit")}</span>
							</Link>

							{/* Status Change Menu */}
							{onStatusChange && (
								<CatStatusMenu
									catId={cat.id}
									currentStatus={cat.status || "available"}
									onStatusChange={(newStatus) =>
										onStatusChange(cat.id, newStatus)
									}
									catName={cat.name}
								/>
							)}

							{/* Delete Button */}
							{onDelete && (
								<Button
									variant="outline"
									onClick={() =>
										onDelete({ id: cat.id, name: cat.name })
									}
									className={cn(
										"inline-flex items-center px-4 py-2 bg-white hover:bg-red-50 text-red-600 hover:text-red-700 rounded-lg font-medium transition-colors min-h-[44px] border border-red-200 hover:border-red-300",
										isRTL
											? "space-x-reverse space-x-2"
											: "space-x-2"
									)}
									disabled={isDeletingCat}
								>
									<Trash2 className="w-4 h-4" />
									<span>{buttonT("delete")}</span>
								</Button>
							)}
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
