"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	<PERSON><PERSON>Footer,
	<PERSON><PERSON>Header,
	DialogTitle,
} from "@/components/ui/dialog";
import { api } from "@/lib/trpc/react";
import { useSearchParams } from "next/navigation";
import { ProfileCatCard } from "@/components/profile/shared/profile-cat-card";
import { Pagination } from "@/components/pagination";
import { useTranslations } from "next-intl";
import { Link } from "@/lib/i18n/navigation";

export function ListedCats() {
	const { toast } = useToast();
	const searchParams = useSearchParams();
	const [dialogOpen, setDialogOpen] = useState(false);
	const [catToDelete, setCatToDelete] = useState<{
		id: string;
		name: string;
	} | null>(null);
	const t = useTranslations("profile");
	const catsT = useTranslations("cats");
	const buttonT = useTranslations("buttons");
	const paginationT = useTranslations("pagination");

	// Get current page from URL or default to 1
	const page = searchParams.get("page")
		? parseInt(searchParams.get("page") as string)
		: 1;
	const limit = 12; // Number of cats per page

	// Get cats from API
	const catsQuery = api.cats.getUserCats.useQuery({
		includeDrafts: true,
		page,
		limit,
	});

	// Delete mutation
	const deleteMutation = api.cats.delete.useMutation();

	// Status update mutation
	const updateStatusMutation = api.cats.updateStatus.useMutation();

	// Handle delete
	const handleDelete = async (cat: { id: string; name: string }) => {
		setCatToDelete(cat);
		setDialogOpen(true);
	};

	// Confirm delete
	const confirmDelete = async () => {
		if (!catToDelete) return;

		try {
			await deleteMutation.mutateAsync(catToDelete.id);
			toast({
				title: t("catDeleted"),
				description: t("catDeletedDescription"),
			});
			catsQuery.refetch();
			setDialogOpen(false);
		} catch (error) {
			toast({
				title: t("errorDeletingCat"),
				description: t("errorDeletingCatDescription"),
				variant: "destructive",
			});
		}
	};

	// Handle status change
	const handleStatusChange = async (id: string, status: string) => {
		try {
			const result = await updateStatusMutation.mutateAsync({
				id,
				status: status as
					| "available"
					| "pending"
					| "adopted"
					| "unavailable",
			});
			toast({
				title: catsT("status.updateSuccess"),
				description: result.message,
			});
			catsQuery.refetch();
		} catch (error) {
			toast({
				title: catsT("status.updateError"),
				description: t("errorUpdatingStatusDescription"),
				variant: "destructive",
			});
		}
	};

	// Loading state
	if (catsQuery.isLoading) {
		return (
			<div className="flex justify-center items-center py-12">
				<Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
				<span className="ml-2">{catsT("loading")}</span>
			</div>
		);
	}

	// Error state
	if (catsQuery.isError) {
		return (
			<div className="text-center py-12 text-red-500">
				<h3 className="text-xl font-medium mb-2">
					{catsT("errors.loadingError")}
				</h3>
				<p className="mb-6">{catsT("errors.genericError")}</p>
				<Button onClick={() => catsQuery.refetch()}>
					{catsT("retry")}
				</Button>
			</div>
		);
	}

	// Empty state
	const cats = catsQuery.data?.cats || [];
	const pagination = catsQuery.data?.pagination || {
		total: 0,
		pageCount: 1,
		page: 1,
		limit,
	};

	if (cats.length === 0) {
		return (
			<div className="text-center py-12">
				<h3 className="text-xl font-medium mb-2">
					{t("noCatsListed")}
				</h3>
				<p className="text-muted-foreground mb-6">
					{t("startAddingCats")}
				</p>
				<Button asChild>
					<Link href="/cats/new">
						<Plus className="h-4 w-4 mr-2" />
						{t("addCat")}
					</Link>
				</Button>
			</div>
		);
	}

	return (
		<div className="w-full">
			<div className="flex justify-between items-center mb-6">
				<p className="text-muted-foreground">
					{paginationT("showing")} {cats.length}{" "}
					{paginationT("of_total")} {pagination.total}{" "}
					{catsT("title").toLowerCase()}
				</p>
			</div>

			<div className="space-y-6">
				{cats.map((cat) => (
					<ProfileCatCard
						key={cat.id}
						cat={cat}
						showActions={true}
						onDelete={handleDelete}
						onStatusChange={handleStatusChange}
						isDeletingCat={deleteMutation.isPending}
					/>
				))}
			</div>

			{pagination.pageCount > 1 && (
				<div className="mt-8">
					<Pagination
						currentPage={pagination.page}
						totalPages={pagination.pageCount}
					/>
				</div>
			)}

			{/* Delete confirmation dialog */}
			{dialogOpen && (
				<Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>{t("confirmDelete")}</DialogTitle>
							<DialogDescription>
								{t("confirmDeleteDescription", {
									catName: catToDelete?.name || "",
								})}
							</DialogDescription>
						</DialogHeader>
						<DialogFooter>
							<Button
								variant="outline"
								onClick={() => setDialogOpen(false)}
							>
								{buttonT("cancel")}
							</Button>
							<Button
								variant="destructive"
								onClick={confirmDelete}
								disabled={deleteMutation.isPending}
							>
								{deleteMutation.isPending ? (
									<>
										<Loader2 className="mr-2 h-4 w-4 animate-spin" />
										{t("deleting")}
									</>
								) : (
									buttonT("delete")
								)}
							</Button>
						</DialogFooter>
					</DialogContent>
				</Dialog>
			)}
		</div>
	);
}
