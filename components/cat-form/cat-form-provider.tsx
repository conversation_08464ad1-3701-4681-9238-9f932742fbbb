"use client";

import { useState, useCallback, createContext, useContext } from "react";
import { useForm, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/lib/trpc/react";
import { useDropzone } from "react-dropzone";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

// Constants
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_IMAGE_TYPES = [
	"image/jpeg",
	"image/jpg",
	"image/png",
	"image/webp",
];
const REDIRECT_DELAY = 3000; // 3 seconds delay before redirect

// Provider component
export function CatFormProvider({
	children,
	cat,
}: {
	children: (context: CatFormContextType) => React.ReactNode;
	cat?: any;
}) {
	const router = useRouter();
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isSavingDraft, setIsSavingDraft] = useState(false);
	const { toast } = useToast();
	const [imageErrors, setImageErrors] = useState<string[]>([]);
	const t = useTranslations("forms.catForm");
	const validationT = useTranslations("forms.catForm.validation");
	const errorsT = useTranslations("forms.catForm.errors");

	// Define the form schema with translations
	const formSchema = z.object({
		name: z
			.string()
			.min(2, {
				message: validationT("nameLength"),
			})
			.trim(),
		gender: z.enum(["male", "female"]),
		age: z.string().min(1, {
			message: validationT("ageRequired"),
		}),
		breedId: z.string({
			message: validationT("breedRequired"),
		}),
		description: z.string().min(20, {
			message: validationT("descriptionLength"),
		}),
		story: z.string().optional(),
		wilayaId: z.string({ message: validationT("wilayaRequired") }).min(1, {
			message: validationT("wilayaRequired"),
		}),
		communeId: z
			.string({ message: validationT("communeRequired") })
			.min(1, {
				message: validationT("communeRequired"),
			}),
		vaccinated: z.boolean().default(false),
		neutered: z.boolean().default(false),
		specialNeeds: z.boolean().default(false),
		specialNeedsDescription: z.string().optional(),
		adopted: z.boolean().default(false),
		isDraft: z.boolean().default(true),
	});

	// Define the form values type
	type FormValues = z.infer<typeof formSchema>;

	// Image upload state
	const [images, setImages] = useState<
		{ file: File | null; preview: string; isPrimary?: boolean }[]
	>(() => {
		if (cat?.images) {
			// Check if any image is marked as primary
			const hasPrimary = cat.images.some((img: any) => img.isPrimary);

			// If no image is marked as primary, mark the first one
			return cat.images.map((img: any, index: number) => ({
				file: null,
				preview: img.url,
				isPrimary: img.isPrimary || (!hasPrimary && index === 0),
			}));
		}
		return [];
	});

	// Fetch breeds, wilayas, and communes using tRPC
	const { data: breeds, isLoading: isLoadingBreeds } =
		api.breeds.getAll.useQuery();
	const { data: wilayas, isLoading: isLoadingWilayas } =
		api.location.getWilayas.useQuery();

	const [selectedWilayaId, setSelectedWilayaId] = useState<
		string | undefined
	>(cat?.wilayaId ? cat.wilayaId.toString() : undefined);

	const { data: communes, isLoading: isLoadingCommunes } =
		api.location.getCommunesByWilaya.useQuery(
			{
				wilayaId: selectedWilayaId
					? parseInt(selectedWilayaId)
					: undefined,
			},
			{ enabled: !!selectedWilayaId }
		);

	// Initialize form with default values
	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema) as any,
		mode: "onChange",
		defaultValues: {
			name: cat?.name || "",
			gender: (cat?.gender || "male") as "male" | "female",
			age: cat?.age || "",
			breedId: cat?.breedId ? cat.breedId.toString() : undefined,
			description: cat?.description || "",
			story: cat?.story || "",
			wilayaId: cat?.wilayaId ? cat.wilayaId.toString() : undefined,
			communeId: cat?.communeId ? cat.communeId.toString() : undefined,
			vaccinated: cat?.vaccinated ?? false,
			neutered: cat?.neutered ?? false,
			specialNeeds: cat?.specialNeeds ?? false,
			specialNeedsDescription: cat?.specialNeedsDescription || "",
			adopted: cat?.adopted ?? false,
			isDraft: cat?.isDraft ?? true,
		},
	});

	const watchSpecialNeeds = form.watch("specialNeeds");
	const watchIsDraft = form.watch("isDraft");

	// Handle wilaya change to load communes
	const handleWilayaChange = async (value: string) => {
		form.setValue("wilayaId", value);
		setSelectedWilayaId(value);
		form.setValue("communeId", ""); // Reset commune when wilaya changes
		await form.trigger(["wilayaId", "communeId"]);
	};

	// Validate info step before proceeding to images
	const validateInfoStep = async () => {
		// Trigger validation for required fields, especially select fields
		const result = await form.trigger([
			"name",
			"age",
			"gender",
			"description",
			"breedId",
			"wilayaId",
			"communeId",
		]);
		return result;
	};

	// Image dropzone setup with validation
	const onDrop = useCallback(
		(acceptedFiles: File[], rejectedFiles: any[] = []) => {
			// Clear previous errors
			setImageErrors([]);

			// Handle rejected files
			if (rejectedFiles.length > 0) {
				const errors: string[] = [];
				rejectedFiles.forEach((file) => {
					file.errors.forEach((error: any) => {
						if (error.code === "file-too-large") {
							errors.push(errorsT("imageSize"));
						} else if (error.code === "file-invalid-type") {
							errors.push(errorsT("imageFormat"));
						}
					});
				});
				setImageErrors(errors);
				return;
			}

			// Check if adding these files would exceed the 5 image limit
			if (images.length + acceptedFiles.length > 5) {
				setImageErrors([errorsT("maxImages")]);
				return;
			}

			// Process accepted files
			setImages((prevImages) => [
				...prevImages,
				...acceptedFiles.map((file) => {
					// Create a preview URL for the image
					const preview = URL.createObjectURL(file);
					return {
						file,
						preview,
						isPrimary:
							prevImages.length === 0 &&
							acceptedFiles.indexOf(file) === 0,
					};
				}),
			]);
		},
		[images, errorsT]
	);

	// Configure dropzone
	const { getRootProps, getInputProps } = useDropzone({
		onDrop,
		accept: {
			"image/jpeg": [],
			"image/jpg": [],
			"image/png": [],
			"image/webp": [],
		},
		maxSize: MAX_FILE_SIZE,
		maxFiles: 5,
	});

	// Remove an image
	const removeImage = (index: number) => {
		setImages((prevImages) => {
			// Create a copy of the images array without the removed image
			const updatedImages = prevImages.filter((_, i) => i !== index);

			// If the removed image was primary and there are still images left,
			// make the first image primary
			if (prevImages[index]?.isPrimary && updatedImages.length > 0) {
				updatedImages[0].isPrimary = true;
			}

			return updatedImages;
		});
	};

	// Set an image as primary
	const setPrimaryImage = (index: number) => {
		setImages((prevImages) =>
			prevImages.map((image, i) => ({
				...image,
				isPrimary: i === index,
			}))
		);
	};

	// Upload an image to the server
	const uploadImage = async (file: File): Promise<string> => {
		try {
			// Create a FormData object to send the file
			const formData = new FormData();
			formData.append("file", file);

			// Send the file to the server
			const response = await fetch("/api/upload", {
				method: "POST",
				body: formData,
			});

			if (!response.ok) {
				throw new Error(errorsT("uploadFailed"));
			}

			const data = await response.json();
			return data.url;
		} catch (error) {
			console.error("Error uploading image:", error);
			throw error;
		}
	};
	// Initialize the mutations
	const createCatMutation = api.cats.create.useMutation();
	const updateCatMutation = api.cats.update.useMutation();

	// Form submission handler
	const onSubmit: SubmitHandler<FormValues> = async (data, e) => {
		e?.preventDefault();
		setIsSubmitting(true);

		try {
			// If the cat has special needs, make sure a description is provided
			if (
				data.specialNeeds &&
				(!data.specialNeedsDescription ||
					data.specialNeedsDescription.trim() === "")
			) {
				form.setError("specialNeedsDescription", {
					type: "manual",
					message: validationT("specialNeedsDescriptionRequired"),
				});
				setIsSubmitting(false);
				return;
			}

			// Ensure at least one image is uploaded for published cats
			if (!data.isDraft && images.length === 0) {
				setImageErrors([errorsT("noImages")]);
				setIsSubmitting(false);
				return;
			}

			// Prepare the image data
			const imageData = await Promise.all(
				images.map(async (image) => {
					// If the image already has a URL (from existing cat), use that
					if (!image.file) {
						return {
							url: image.preview,
							isPrimary: image.isPrimary,
						};
					}

					// Otherwise, upload the image and get the URL
					try {
						const url = await uploadImage(image.file);
						return {
							url,
							isPrimary: image.isPrimary,
						};
					} catch (error) {
						console.error("Error uploading image:", error);
						throw error;
					}
				})
			);

			// Prepare the cat data
			const catData = {
				...data,
				images: imageData,
			};

			// Update or create the cat
			if (cat?.id) {
				// Update existing cat
				await updateCatMutation.mutateAsync({
					id: cat.id.toString(),
					data: catData as any,
				});

				toast({
					title: t("success.updated"),
				});
			} else {
				// Create new cat
				await createCatMutation.mutateAsync(catData as any);

				toast({
					title: data.isDraft
						? t("success.draftSaved")
						: t("success.published"),
				});
			}

			// Redirect after a short delay
			setTimeout(() => {
				router.push(`/profile`);
				router.refresh();
			}, REDIRECT_DELAY);
		} catch (error) {
			console.error("Error submitting form:", error);
			toast({
				title: "Error",
				description: "Failed to save cat. Please try again.",
				variant: "destructive",
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	// Save as draft
	async function saveDraft() {
		setIsSavingDraft(true);
		form.setValue("isDraft", true);
		await form.handleSubmit(onSubmit)();
		setIsSavingDraft(false);
	}

	// Publish cat
	function publishCat() {
		// Set isDraft to false and submit the form
		form.setValue("isDraft", false);
		form.handleSubmit(onSubmit)();
	}

	// Create the context value
	const contextValue: CatFormContextType = {
		form,
		isSubmitting,
		isSavingDraft,
		images,
		setImages,
		onDrop,
		getRootProps,
		getInputProps,
		removeImage,
		setPrimaryImage,
		publishCat,
		saveDraft,
		selectedWilayaId,
		setSelectedWilayaId,
		handleWilayaChange,
		breeds,
		isLoadingBreeds,
		wilayas,
		isLoadingWilayas,
		communes,
		isLoadingCommunes,
		watchSpecialNeeds,
		watchIsDraft,
		cat,
		imageErrors,
		validateInfoStep,
	};

	return (
		<CatFormContext.Provider value={contextValue}>
			{children(contextValue)}
		</CatFormContext.Provider>
	);
}

// Define the context type
export interface CatFormContextType {
	form: any;
	isSubmitting: boolean;
	isSavingDraft: boolean;
	images: Array<{ file: File | null; preview: string; isPrimary?: boolean }>;
	setImages: React.Dispatch<
		React.SetStateAction<
			Array<{ file: File | null; preview: string; isPrimary?: boolean }>
		>
	>;
	onDrop: (acceptedFiles: File[]) => void;
	getRootProps: ReturnType<typeof useDropzone>["getRootProps"];
	getInputProps: ReturnType<typeof useDropzone>["getInputProps"];
	removeImage: (index: number) => void;
	setPrimaryImage: (index: number) => void;
	publishCat: () => void;
	saveDraft: () => Promise<void>;
	selectedWilayaId: string | undefined;
	setSelectedWilayaId: React.Dispatch<
		React.SetStateAction<string | undefined>
	>;
	handleWilayaChange: (value: string) => void;
	breeds: any;
	isLoadingBreeds: boolean;
	wilayas: any;
	isLoadingWilayas: boolean;
	communes: any;
	isLoadingCommunes: boolean;
	watchSpecialNeeds: boolean;
	watchIsDraft: boolean;
	cat?: any;
	imageErrors: string[];
	validateInfoStep: () => Promise<boolean>;
}

// Create context
const CatFormContext = createContext<CatFormContextType | null>(null);

// Custom hook to use the cat form context
export function useCatForm() {
	const context = useContext(CatFormContext);
	if (!context) {
		throw new Error("useCatForm must be used within a CatFormProvider");
	}
	return context;
}
