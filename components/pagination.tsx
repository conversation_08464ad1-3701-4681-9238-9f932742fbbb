"use client";

import {
	Pagination as ShadcnPagination,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";
import {
	useRouter,
	usePathname as useNextPathname,
	useSearchParams,
} from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { usePathname } from "@/lib/i18n/navigation";

export type PaginationProps = {
	currentPage: number;
	totalPages: number;
};

export function Pagination({ currentPage, totalPages }: PaginationProps) {
	const router = useRouter();
	const nextPathname = useNextPathname();
	const pathname = usePathname();
	const searchParams = useSearchParams();
	const t = useTranslations("pagination");
	const locale = useLocale();

	const dir = locale === "ar" ? "rtl" : "ltr";

	if (totalPages <= 1) {
		return null;
	}

	const handlePageChange = (page: number) => {
		if (page === currentPage) return;

		const params = new URLSearchParams(searchParams.toString());
		params.set("page", page.toString());
		router.push(`${nextPathname}?${params.toString()}`);
	};

	const renderPageNumbers = () => {
		const pages = [];
		const showEllipsisStart = currentPage > 3;
		const showEllipsisEnd = currentPage < totalPages - 2;

		// First page (always show)
		pages.push(
			<PaginationItem key="1">
				<PaginationLink
					dir={dir}
					href={`${pathname}?page=1`}
					isActive={currentPage === 1}
					onClick={(e) => {
						e.preventDefault();
						handlePageChange(1);
					}}
					aria-label={t("firstPage")}
				>
					1
				</PaginationLink>
			</PaginationItem>
		);

		// Start ellipsis
		if (showEllipsisStart) {
			pages.push(
				<PaginationItem key="ellipsis-start">
					<PaginationEllipsis />
				</PaginationItem>
			);
		}

		// Current page and surrounding pages
		for (
			let i = Math.max(2, currentPage - 1);
			i <= Math.min(totalPages - 1, currentPage + 1);
			i++
		) {
			if (i === 1 || i === totalPages) continue; // Skip first and last as they're handled separately
			pages.push(
				<PaginationItem key={i}>
					<PaginationLink
						dir={dir}
						href={`${pathname}?page=${i}`}
						isActive={i === currentPage}
						onClick={(e) => {
							e.preventDefault();
							handlePageChange(i);
						}}
						aria-label={`${t("page")} ${i}`}
					>
						{i}
					</PaginationLink>
				</PaginationItem>
			);
		}

		// End ellipsis
		if (showEllipsisEnd) {
			pages.push(
				<PaginationItem key="ellipsis-end">
					<PaginationEllipsis />
				</PaginationItem>
			);
		}

		// Last page (always show)
		pages.push(
			<PaginationItem key={totalPages}>
				<PaginationLink
					dir={dir}
					href={`${pathname}?page=${totalPages}`}
					isActive={currentPage === totalPages}
					onClick={(e) => {
						e.preventDefault();
						handlePageChange(totalPages);
					}}
					aria-label={t("lastPage")}
				>
					{totalPages}
				</PaginationLink>
			</PaginationItem>
		);

		return pages;
	};

	return (
		<ShadcnPagination className="mt-4">
			<PaginationContent>
				{currentPage > 1 && (
					<PaginationItem>
						<PaginationPrevious
							dir={dir}
							href={`${pathname}?page=${currentPage - 1}`}
							onClick={(e) => {
								e.preventDefault();
								handlePageChange(currentPage - 1);
							}}
							aria-label={t("previous")}
						/>
					</PaginationItem>
				)}

				{renderPageNumbers()}

				{currentPage < totalPages && (
					<PaginationItem>
						<PaginationNext
							dir={dir}
							href={`${pathname}?page=${currentPage + 1}`}
							onClick={(e) => {
								e.preventDefault();
								handlePageChange(currentPage + 1);
							}}
							aria-label={t("next")}
						/>
					</PaginationItem>
				)}
			</PaginationContent>
		</ShadcnPagination>
	);
}
