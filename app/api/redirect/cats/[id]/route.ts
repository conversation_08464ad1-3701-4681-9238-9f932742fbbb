import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { cats } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import {
	getCatSlug,
	setCatSlug,
	catRedirectCache,
} from "@/lib/utils/redirect-cache";

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
) {
	try {
		const { id } = await params;
		const { searchParams } = new URL(request.url);
		const locale = searchParams.get("locale") || "en";
		const isEdit = searchParams.get("edit") === "true";

		// Validate ID is numeric
		const catId = parseInt(id);
		if (isNaN(catId)) {
			console.log(`[API] Invalid cat ID: ${id}`);
			return NextResponse.redirect(
				new URL(`/${locale}/404`, request.url),
				301
			);
		}

		// Check cache first
		const cachedSlug = getCatSlug(catId);
		if (cachedSlug) {
			console.log(`[API] Cache hit for cat ID ${catId}: ${cachedSlug}`);

			// Validate that cached value is actually a slug, not an ID
			// Purely numeric values (without prefix) are likely IDs, not legitimate slugs
			// Legitimate numeric cat names get prefixed (e.g., "cat-4444")
			if (/^\d+$/.test(cachedSlug)) {
				console.warn(
					`[API] Cached value for cat ID ${catId} appears to be an unprefixed numeric value (${cachedSlug}), likely an ID not a slug. Clearing cache and querying database.`
				);
				// Clear the invalid cache entry
				catRedirectCache.delete(`cat-${catId}`);
			} else {
				const newPath = `/${locale}/cats/${cachedSlug}${isEdit ? "/edit" : ""}`;

				// Preserve any additional query parameters (excluding our internal ones)
				const preservedParams = new URLSearchParams();
				for (const [key, value] of searchParams.entries()) {
					if (key !== "locale" && key !== "edit") {
						preservedParams.append(key, value);
					}
				}

				const finalUrl = new URL(
					newPath +
						(preservedParams.toString()
							? `?${preservedParams.toString()}`
							: ""),
					request.url
				);

				return NextResponse.redirect(finalUrl, 301);
			}
		}

		console.log(`[API] Cache miss for cat ID ${catId}, querying database`);

		// Query database for cat slug
		const cat = await db.query.cats.findFirst({
			where: eq(cats.id, catId),
			columns: { slug: true },
		});

		if (!cat?.slug) {
			console.log(`[API] No cat found with ID ${catId}`);
			return NextResponse.redirect(
				new URL(`/${locale}/404`, request.url),
				301
			);
		}

		// Validate that the slug from database is legitimate
		// Purely numeric slugs (without prefix) indicate data corruption or migration issues
		// Legitimate numeric cat names should have been prefixed (e.g., "cat-4444")
		if (/^\d+$/.test(cat.slug)) {
			console.warn(
				`[API] Database returned unprefixed numeric slug (${cat.slug}) for cat ID ${catId}. This indicates data corruption or incomplete migration.`
			);

			// This is likely old data that wasn't properly migrated
			// We should regenerate the slug to fix the issue
			console.error(
				`[API] CRITICAL: Cat ID ${catId} has unprefixed numeric slug "${cat.slug}". This needs data migration!`
			);

			// Don't cache the problematic slug
			// Instead, redirect to 404 and log for manual data fix
			console.log(
				`[API] Redirecting to 404 for cat ID ${catId} - requires data migration to fix numeric slug`
			);
			return NextResponse.redirect(
				new URL(`/${locale}/404`, request.url),
				301
			);
		}

		// Cache the result (only if it's a valid slug)
		setCatSlug(catId, cat.slug);
		console.log(`[API] Cached cat ID ${catId}: ${cat.slug}`);

		const newPath = `/${locale}/cats/${cat.slug}${isEdit ? "/edit" : ""}`;

		// Preserve any additional query parameters (excluding our internal ones)
		const preservedParams = new URLSearchParams();
		for (const [key, value] of searchParams.entries()) {
			if (key !== "locale" && key !== "edit") {
				preservedParams.append(key, value);
			}
		}

		const finalUrl = new URL(
			newPath +
				(preservedParams.toString()
					? `?${preservedParams.toString()}`
					: ""),
			request.url
		);

		console.log(
			`[API] Redirecting cat ID ${catId} to ${finalUrl.pathname}${finalUrl.search}`
		);
		return NextResponse.redirect(finalUrl, 301);
	} catch (error) {
		console.error("[API] Error in cat redirect:", error);

		// Fallback to 404 on any error
		const { searchParams } = new URL(request.url);
		const locale = searchParams.get("locale") || "en";
		return NextResponse.redirect(
			new URL(`/${locale}/404`, request.url),
			301
		);
	}
}

// Optional: Add cache statistics endpoint for debugging
export async function HEAD(request: NextRequest) {
	const cacheStats = catRedirectCache.getStats();

	return new NextResponse(null, {
		status: 200,
		headers: {
			"X-Cache-Stats": JSON.stringify(cacheStats),
		},
	});
}
