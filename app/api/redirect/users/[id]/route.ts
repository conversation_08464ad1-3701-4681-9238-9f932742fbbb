import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import {
	getUserSlug,
	setUserSlug,
	userRedirectCache,
} from "@/lib/utils/redirect-cache";

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
) {
	try {
		const { id } = await params;
		const { searchParams } = new URL(request.url);
		const locale = searchParams.get("locale") || "en";

		// Validate ID is numeric
		const userId = parseInt(id);
		if (isNaN(userId)) {
			console.log(`[API] Invalid user ID: ${id}`);
			return NextResponse.redirect(
				new URL(`/${locale}/404`, request.url),
				301
			);
		}

		// Check cache first
		const cachedSlug = getUserSlug(userId);
		if (cachedSlug) {
			console.log(`[API] Cache hit for user ID ${userId}: ${cachedSlug}`);

			// Validate that cached value is actually a slug, not an ID
			// Purely numeric values (without prefix) are likely IDs, not legitimate slugs
			// Note: Users don't get numeric prefixes like cats, so this is simpler
			if (/^\d+$/.test(cachedSlug)) {
				console.warn(
					`[API] Cached value for user ID ${userId} appears to be an unprefixed numeric value (${cachedSlug}), likely an ID not a slug. Clearing cache and querying database.`
				);
				// Clear the invalid cache entry
				userRedirectCache.delete(`user-${userId}`);
			} else {
				const newPath = `/${locale}/users/${cachedSlug}`;

				// Preserve any additional query parameters (excluding our internal ones)
				const preservedParams = new URLSearchParams();
				for (const [key, value] of searchParams.entries()) {
					if (key !== "locale") {
						preservedParams.append(key, value);
					}
				}

				const finalUrl = new URL(
					newPath +
						(preservedParams.toString()
							? `?${preservedParams.toString()}`
							: ""),
					request.url
				);

				return NextResponse.redirect(finalUrl, 301);
			}
		}

		console.log(
			`[API] Cache miss for user ID ${userId}, querying database`
		);

		// Query database for user slug
		const user = await db.query.users.findFirst({
			where: eq(users.id, userId),
			columns: { slug: true },
		});

		if (!user?.slug) {
			console.log(`[API] No user found with ID ${userId}`);
			return NextResponse.redirect(
				new URL(`/${locale}/404`, request.url),
				301
			);
		}

		// Validate that the slug from database is legitimate
		// Purely numeric slugs indicate data corruption or migration issues
		// Users with numeric names should still get proper non-numeric slugs
		if (/^\d+$/.test(user.slug)) {
			console.warn(
				`[API] Database returned unprefixed numeric slug (${user.slug}) for user ID ${userId}. This indicates data corruption or incomplete migration.`
			);

			// This is likely old data that wasn't properly migrated
			console.error(
				`[API] CRITICAL: User ID ${userId} has unprefixed numeric slug "${user.slug}". This needs data migration!`
			);

			// Don't cache the problematic slug
			// Instead, redirect to 404 and log for manual data fix
			console.log(
				`[API] Redirecting to 404 for user ID ${userId} - requires data migration to fix numeric slug`
			);
			return NextResponse.redirect(
				new URL(`/${locale}/404`, request.url),
				301
			);
		}

		// Cache the result (only if it's a valid slug)
		setUserSlug(userId, user.slug);
		console.log(`[API] Cached user ID ${userId}: ${user.slug}`);

		const newPath = `/${locale}/users/${user.slug}`;

		// Preserve any additional query parameters (excluding our internal ones)
		const preservedParams = new URLSearchParams();
		for (const [key, value] of searchParams.entries()) {
			if (key !== "locale") {
				preservedParams.append(key, value);
			}
		}

		const finalUrl = new URL(
			newPath +
				(preservedParams.toString()
					? `?${preservedParams.toString()}`
					: ""),
			request.url
		);

		console.log(
			`[API] Redirecting user ID ${userId} to ${finalUrl.pathname}${finalUrl.search}`
		);
		return NextResponse.redirect(finalUrl, 301);
	} catch (error) {
		console.error("[API] Error in user redirect:", error);

		// Fallback to 404 on any error
		const { searchParams } = new URL(request.url);
		const locale = searchParams.get("locale") || "en";
		return NextResponse.redirect(
			new URL(`/${locale}/404`, request.url),
			301
		);
	}
}

// Optional: Add cache statistics endpoint for debugging
export async function HEAD(request: NextRequest) {
	const cacheStats = userRedirectCache.getStats();

	return new NextResponse(null, {
		status: 200,
		headers: {
			"X-Cache-Stats": JSON.stringify(cacheStats),
		},
	});
}
